# Jigsaw Finance / jigsaw-contracts - Cantina Bug Bounty Competition

**Competition URL:** https://cantina.xyz/competitions/7a40c849-0b35-4128-b084-d9a83fd533ea

---

## Overview

Jigsaw is a CDP-based stablecoin protocol that brings full flexibility and composability to your collateral through the concept of "dynamic collateral". Jigsaw leverages crypto's unique permissionless composability to enable dynamic collateral in a fully non-custodial way. 

Technically it comprises of two separate repositories: 
- **jigsaw-protocol-v1** - the core of the protocol managing deposits, vaults, minting, etc
- **jigsaw-strategies-v1** - allows investing of the deposited assets

It's very important to ensure safe integration of one into another.

## Prize Distribution and Scoring

- **Public Prize Pool:** $60,000
- **Additional pay for dedicated Cantina Fellow:** $10,000
- Scoring described in the competition scoring page
- Findings Severities described in detail on docs page

## Documentation

- [Jigsaw Protocol](https://docs.jigsaw.finance/)
- [Jigsaw Protocol v1](https://github.com/jigsaw-finance/jigsaw-protocol-v1)
- [Jigsaw Strategies v1](https://github.com/jigsaw-finance/jigsaw-strategies-v1)

## Important Note

⚠️ **The Contracts are live.** Team will fix any criticals reported that needs to be addressed. These findings would immediately be considered out of scope once they are fixed publicly.

## Scope

### Repository 1: jigsaw-protocol-v1
- **Repository:** https://github.com/jigsaw-finance/jigsaw-protocol-v1/tree/dev/src
- **Commit:** `e8f373e0f5c7416bd0acee800223e6fd6a959494`
- **Files in scope:**
  - `src/Holding.sol`
  - `src/HoldingManager.sol`
  - `src/JigsawUSD.sol`
  - `src/LiquidationManager.sol`
  - `src/Manager.sol`
  - `src/ReceiptToken.sol`
  - `src/ReceiptTokenFactory.sol`
  - `src/SharesRegistry.sol`
  - `src/StablesManager.sol`
  - `src/StrategyManager.sol`
  - `src/SwapManager.sol`

### Repository 2: jigsaw-strategies-v1
- **Repository:** https://github.com/jigsaw-finance/jigsaw-strategies-v1
- **Commit:** `7009245f735aa49d8b2e8d8debc5d7c3f01b75a5`
- **Files in scope:**
  - `src/aave/AaveV3StrategyV2.sol`
  - `src/dinero/DineroStrategyV2.sol`
  - `src/elixir/ElixirStrategy.sol`
  - `src/extensions/FeeManager.sol`
  - `src/libraries/OperationsLib.sol`
  - `src/libraries/StrategyConfigLib.sol`
  - `src/pendle/PendleStrategyV2.sol`
  - `src/reservoir/ReservoirSavingStrategyV2.sol`
  - `src/staker/StakerLight.sol`
  - `src/staker/StakerLightFactory.sol`
  - `src/StrategyBaseUpgradeableV2.sol`

## Out of Scope

### Previous Security Reports
- See READMEs for details

### Expected Behaviors (Trusted/Untrusted Roles and/or Accepted Risks)
- Owner is trusted multisig
- Collateral changes not being tracked while invested is accepted risk
- Performance fee can be skipped for Pendle rewards
- Rewards accumulated during a period of zero totalSupply cannot be distributed and will remain locked within the StakerLight
- Manager cannot be updated in Jigsaw USD

### Automated Findings by LightChaser
- **Protocol:** https://gist.github.com/ChaseTheLight01/c33b9dccb4712c62f61f83108edb64a8
- **Strategy:** https://gist.github.com/ChaseTheLight01/5bcf54ff60d52bf4c18709ecee332f63

### Fixed Findings (Out of Scope)
- The following finding has been fixed and would be considered out of scope after the public fix:
- `Unliquidatable Bad-Debt via Strategy Negative Yield in Liquidation Routines`
- **Fix Link:** [Fix](https://github.com/jigsaw-finance/jigsaw-protocol-v1/commit/fix-commit-hash)

## Basic POC Test Requirements

- **Mandatory POC rule applies to this competition**
- Use `BasicContractsFixture` contract's `init()` in `test/fixtures/BasicContractsFixture.t.sol` for both repositories
- **Note:** Ensure `.env` is filled in correctly, please see the `.env.example` file

## Contact Information

For any issues or concerns regarding this competition, please reach out to the Cantina core team through the **Cantina Discord**.

## Competition Details

- **Status:** Live
- **Total Reward:** $60,000
- **Findings Submitted:** 683
- **Start Date:** 21 May 2025 5:00pm (local time)
- **End Date:** 19 Jun 2025 8:00pm (local time)

---

**Note:** You need to be logged in as a researcher in order to join the competition.

© 2025 Cantina. All rights reserved.
