# Vulnerability: Reentrancy in StablesManager Registry Update

## 🎯 **Classification**
- **Category**: External Calls / State Management
- **Severity**: Medium
- **Exploitability**: Low
- **Impact**: Medium

## 📍 **Location**
- **File**: `src/StablesManager.sol`
- **Function**: `registerOrUpdateShareRegistry(address _registry, address _token, bool _active)`
- **Lines**: 303-320
- **Impact**: State variable written after external call, potential for cross-function reentrancy
- **Related Critical Function**: Registry management affects all collateral operations

## 🔍 **Vulnerability Description**
**What I Found**: The `registerOrUpdateShareRegistry` function modifies the `shareRegistryInfo[_token]` state variable after making an external call to `manager.addWithdrawableToken(_token)`, creating a potential for reentrancy attacks.

**Why It's Vulnerable**: The function calls an external contract (manager) before updating its internal state. If the manager contract or any contract it calls can callback into StablesManager, it could lead to inconsistent state.

**Attack Vector**: A malicious manager or compromised external contract could:
1. Call back into StablesManager during `addWithdrawableToken`
2. Observe an inconsistent state where the registry call was made but `shareRegistryInfo` not yet updated
3. Potentially manipulate other functions that depend on registry state

## 📋 **Code Analysis**
```solidity
function registerOrUpdateShareRegistry(address _registry, address _token, bool _active) external onlyOwner {
    require(_token != address(0), "3007");
    require(_token == ISharesRegistry(_registry).token(), "3008");

    ShareRegistryInfo memory info;
    info.active = _active;

    if (shareRegistryInfo[_token].deployedAt == address(0)) {
        info.deployedAt = _registry;
        manager.addWithdrawableToken(_token); // ❌ EXTERNAL CALL
        emit RegistryAdded({ token: _token, registry: _registry });
    } else {
        info.deployedAt = shareRegistryInfo[_token].deployedAt;
        emit RegistryUpdated({ token: _token, registry: _registry });
    }

    shareRegistryInfo[_token] = info; // ❌ STATE UPDATE AFTER EXTERNAL CALL
}
```

**Technical Analysis**:
- **Root Cause**: State variable `shareRegistryInfo[_token]` is written after external call
- **Prerequisites**: Malicious or compromised manager contract
- **Impact Assessment**: Could lead to inconsistent registry state during cross-function calls

## 🚨 **Exploitability Assessment**
- **Can Regular User Exploit**: No - requires owner privileges
- **Requires Admin Access**: Yes - function has `onlyOwner` modifier
- **Economic Feasibility**: Low - requires compromised admin/manager contracts
- **Technical Difficulty**: Medium - requires understanding of cross-function reentrancy

## 💰 **Financial Impact**
- **Funds at Risk**: Potentially affects all collateral operations if registry state is manipulated
- **Attack Cost**: Requires controlling manager contract
- **Profit Potential**: Indirect - could enable other attacks through state manipulation

## 🔄 **Connection to Critical Functions**
The `shareRegistryInfo` mapping is used in multiple critical functions:
- `_getRegistry()` - Retrieved by all collateral operations
- `addCollateral()` - Checks registry state
- `borrow()` - Validates against registry
- `removeCollateral()` - Uses registry information
- `repay()` - Interacts with registry

If this state can be manipulated during reentrancy, it could affect all these operations.

## ✅ **Scope Assessment**
**In Scope**: Borderline - While requiring admin access, this represents a state management issue that could be exploited if admin contracts are compromised.

**Risk Level**: Medium - Limited exploitability but potential for significant impact on protocol state.

## 🛠️ **Proposed Fix**
Move state update before external call:
```solidity
function registerOrUpdateShareRegistry(address _registry, address _token, bool _active) external onlyOwner {
    require(_token != address(0), "3007");
    require(_token == ISharesRegistry(_registry).token(), "3008");

    ShareRegistryInfo memory info;
    info.active = _active;

    if (shareRegistryInfo[_token].deployedAt == address(0)) {
        info.deployedAt = _registry;
        
        // Update state BEFORE external call
        shareRegistryInfo[_token] = info;
        
        // Then make external call
        manager.addWithdrawableToken(_token);
        emit RegistryAdded({ token: _token, registry: _registry });
    } else {
        info.deployedAt = shareRegistryInfo[_token].deployedAt;
        shareRegistryInfo[_token] = info;
        emit RegistryUpdated({ token: _token, registry: _registry });
    }
}
```

--- 