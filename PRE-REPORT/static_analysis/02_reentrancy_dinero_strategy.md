# Vulnerability: Reentrancy in DineroStrategyV2 Withdraw Function

## 🎯 **Classification**
- **Category**: External Calls
- **Severity**: High
- **Exploitability**: High
- **Impact**: High

## 📍 **Location**
- **File**: `src/dinero/DineroStrategyV2.sol`
- **Function**: `withdraw(uint256 _shares, address _recipient, address _asset, bytes calldata)`
- **Lines**: 199-290
- **Impact**: State variables modified after external calls that send ETH, enabling reentrancy attacks
- **Related Critical Function**: Strategy withdrawal mechanism affects core liquidation and user fund management

## 🔍 **Vulnerability Description**
**What I Found**: The `withdraw` function in DineroStrategyV2 modifies critical state variables (`recipients[_recipient].totalShares` and `recipients[_recipient].investedAmount`) after making external calls that can send ETH, creating a reentrancy vulnerability.

**Why It's Vulnerable**: The function makes multiple external calls including `IWETH9(tokenIn).deposit{value: postFeeAmount}()` which sends ETH, but updates state variables after these calls. This allows malicious contracts to reenter and manipulate the state.

**Attack Vector**: An attacker could:
1. Create a malicious contract as `_recipient`
2. Call withdraw with shares
3. During the ETH transfer, the malicious contract's receive function is triggered
4. Reenter the withdraw function before state variables are updated
5. Withdraw more shares than they should be allowed to

## 📋 **Code Analysis**
```solidity
function withdraw(
    uint256 _shares,
    address _recipient,
    address _asset,
    bytes calldata
) external override nonReentrant onlyStrategyManager returns (uint256, uint256, int256, uint256) {
    // ... calculations and validations ...
    
    // External calls that can trigger reentrancy:
    _burn({...}); // Burns receipt tokens (external call)
    
    (, bytes memory returnData) = _genericCall({
        _holding: _recipient,
        _contract: address(autoPirexEth),
        _call: abi.encodeCall(IAutoPxEth.redeem, (_shares, address(this), _recipient))
    }); // External call to user-controlled holding
    
    (uint256 postFeeAmount,) = pirexEth.instantRedeemWithPxEth(...); // External call
    
    IWETH9(tokenIn).deposit{value: postFeeAmount}(); // SENDS ETH - reentrancy point
    IERC20(tokenIn).safeTransfer(_recipient, postFeeAmount); // External call
    
    // ... more external calls for fee handling ...
    
    // STATE VARIABLES MODIFIED AFTER EXTERNAL CALLS:
    recipients[_recipient].totalShares -= params.shares; // ❌ VULNERABLE
    recipients[_recipient].investedAmount = ...; // ❌ VULNERABLE
}
```

**Technical Analysis**:
- **Root Cause**: State variables updated after external calls that can send ETH
- **Prerequisites**: Attacker needs to be a legitimate user with shares and control a holding contract
- **Impact Assessment**: Could lead to multiple withdrawals with same shares, draining strategy funds

## 🚨 **Exploitability Assessment**
- **Can Regular User Exploit**: Yes - any user with shares can potentially exploit this
- **Requires Admin Access**: No - user-triggered function
- **Economic Feasibility**: High - can withdraw more assets than entitled to
- **Technical Difficulty**: Medium - requires understanding of reentrancy and contract deployment

## 💰 **Financial Impact**
- **Funds at Risk**: All assets in the DineroStrategyV2 contract
- **Attack Cost**: Gas fees plus legitimate investment to get shares
- **Profit Potential**: Could potentially withdraw multiples of actual investment

## 🔄 **Connection to Critical Functions**
This vulnerability directly impacts:
- Strategy withdrawal mechanisms used in liquidations
- User fund security in yield-earning strategies
- Protocol's ability to maintain accurate accounting of invested assets

## ✅ **Scope Assessment**
**In Scope**: Yes - This is a user-exploitable vulnerability that doesn't require admin privileges and could lead to significant fund loss.

**Risk Level**: Critical - Direct user fund loss potential with clear attack vector.

## 🛠️ **Proposed Fix**
Move state variable updates before external calls:
```solidity
// Update state first
recipients[_recipient].totalShares -= params.shares;
recipients[_recipient].investedAmount = ...;

// Then make external calls
IWETH9(tokenIn).deposit{value: postFeeAmount}();
IERC20(tokenIn).safeTransfer(_recipient, postFeeAmount);
```

--- 