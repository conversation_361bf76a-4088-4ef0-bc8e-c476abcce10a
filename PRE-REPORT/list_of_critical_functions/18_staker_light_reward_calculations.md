# Critical Function Analysis: StakerLight Reward Calculations

## Location
- **File**: `jigsaw-strategies-v1/src/staker/StakerLight.sol`
- **Lines**: 311-357
- **Functions**: `rewardPerToken()`, `earned()`, `addRewards()`

## Function Purpose
Core reward distribution system that calculates per-token rewards and tracks user earnings across time periods. These functions determine how Jigsaw tokens are distributed to strategy participants.

## Critical Mathematical Operations

### 1. **Reward Per Token Calculation**
```solidity
function rewardPerToken() public view override returns (uint256) {
    if (_totalSupply == 0) return rewardPerTokenStored;
    
    return
        rewardPerTokenStored + (((lastTimeRewardApplicable() - lastUpdateTime) * rewardRate * 1e18) / _totalSupply);
}
```
- **Risk**: Division by zero check only for `_totalSupply`, but complex calculation follows
- **Mathematical Impact**: Determines base rate for all reward calculations
- **Precision**: Uses 1e18 scaling factor for precision

### 2. **Individual Earnings Calculation**
```solidity
function earned(address _account) public view override returns (uint256) {
    return
        ((_balances[_account] * (rewardPerToken() - userRewardPerTokenPaid[_account])) / 1e18) + rewards[_account];
}
```
- **Risk**: Large multiplication followed by division can lose precision
- **Mathematical Impact**: Determines individual user reward amounts
- **Edge Case**: Small balances with high rates could round to zero

### 3. **Reward Rate Calculation (Add Rewards)**
```solidity
if (block.timestamp >= periodFinish) {
    rewardRate = _amount / duration;
    periodFinish = block.timestamp + duration;
} else {
    uint256 remaining = periodFinish - block.timestamp;
    uint256 leftover = remaining * rewardRate;
    rewardRate = (_amount + leftover) / remaining;
}
```
- **Risk**: Integer division can lose precision in rate calculation
- **Mathematical Impact**: Affects all future reward calculations
- **Edge Case**: Very small amounts or short periods could round rate to zero

## Why These Functions are Critical

### **1. Reward Distribution Core**
- Determines ALL user rewards across strategies
- Controls token emission rates and timing
- Affects user incentives and strategy economics
- Central to protocol's tokenomics

### **2. Mathematical Vulnerabilities**
- **Precision Loss**: Multiple division operations lose fractional rewards
- **Rate Calculation Errors**: Incorrect rates affect all users
- **Timing Dependencies**: Block timestamp manipulation affects calculations
- **Zero Division Risks**: Edge cases could cause reverts

### **3. User Fund Impact**
- **Direct**: Determines exact reward token amounts
- **Severity**: High - affects all strategy participants
- **Scope**: All users staking in any strategy

## Exploitation Scenarios

### **Scenario 1: Precision Loss Exploitation**
1. Attacker deposits very small amounts frequently
2. Each calculation loses precision due to division
3. Lost precision accumulates as "dust" in contract
4. **Impact**: Users receive fewer rewards than deserved

### **Scenario 2: Rate Manipulation**
1. Admin adds very small reward amounts with short durations
2. Rate calculation rounds to zero due to precision loss
3. No rewards distributed despite tokens being deposited
4. **Impact**: Reward tokens stuck in contract

### **Scenario 3: Timing Attack**
1. User makes large deposits just before reward period ends
2. Earns disproportionate rewards for minimal time
3. Withdraws immediately after reward calculation
4. **Impact**: Unfair reward distribution

### **Scenario 4: Division by Zero Edge Case**
1. All users withdraw simultaneously (`_totalSupply = 0`)
2. New rewards added before any deposits
3. Rate calculations may fail or distribute incorrectly
4. **Impact**: Reward system breakdown

## Economic Impact
- **Severity**: HIGH
- **User Funds at Risk**: All reward tokens in staking contracts
- **Attack Cost**: Low - just transaction costs
- **Profit Potential**: Medium - reward manipulation and theft

## Technical Details
- **Function Types**: Public view and external functions
- **Access Control**: addRewards only by owner
- **Dependencies**: 
  - Block timestamp for timing
  - Total supply tracking
  - Individual balance tracking
- **Mathematical Operations**: 4+ precision-sensitive calculations per reward cycle
- **State Changes**: Updates global reward state

## Precision Constants
- Uses `1e18` as scaling factor for reward calculations
- No bounds checking on reward rates or amounts
- Duration must be > 0 but no upper bounds

## Usage in Protocol
Core to all strategy reward systems:
- Called on every deposit/withdraw to update rewards
- Determines user reward amounts for claiming
- Controls emission rate across all strategies

Any mathematical errors directly impact ALL user rewards across the entire protocol. 