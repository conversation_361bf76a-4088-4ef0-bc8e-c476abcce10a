# Critical Function Analysis: ReservoirSavingStrategyV2.getAssetsToWithdraw()

## Location
- **File**: `jigsaw-strategies-v1/src/reservoir/ReservoirSavingStrategyV2.sol`
- **Lines**: 356-374
- **Function**: `getAssetsToWithdraw(uint256 _shares, uint256 _currentPrice, uint256 _redeemFee)`

## Function Purpose
Critical mathematical function that calculates the exact amount of assets that can be withdrawn for a given number of shares, accounting for Reservoir protocol's dynamic pricing and redemption fees. Uses an iterative approach to find the maximum valid withdrawal amount.

## Critical Mathematical Operations

### 1. **Initial Estimation**
```solidity
assetsToWithdraw = (_shares * _currentPrice * RESERVOIR_FEE_PRECISION)
    / (RESERVOIR_PRICE_PRECISION * (RESERVOIR_FEE_PRECISION + _redeemFee));
```
- **Risk**: Complex multi-factor calculation with multiple precision constants
- **Mathematical Impact**: Provides starting point for iterative refinement
- **Precision**: Uses two different precision constants that must be coordinated

### 2. **Iterative Refinement Loop**
```solidity
while (_previewRedeem(assetsToWithdraw, _currentPrice, _redeemFee) > _shares) {
    assetsToWithdraw--;
}
```
- **Risk**: Unbounded loop could cause gas exhaustion
- **Mathematical Impact**: Decrements until finding maximum valid withdrawal
- **Edge Case**: Large miscalculations could require many iterations

### 3. **Preview Redeem Calculation (Internal)**
```solidity
srUSDToBurn = Math.ceilDiv(_amount * RESERVOIR_PRICE_PRECISION, _currentPrice);
srUSDToBurn = srUSDToBurn * (1e6 + _redeemFee) / 1e6;
```
- **Risk**: Ceiling division followed by fee multiplication
- **Mathematical Impact**: Calculates shares needed for redemption with fees
- **Precision**: Multiple rounding operations that compound

## Why This Function is Critical

### **1. Withdrawal Accuracy**
- Determines exact redemption amounts for all Reservoir withdrawals
- Must be precise to avoid failed transactions or value loss
- Directly affects user's received amounts
- Core to strategy's economic model

### **2. Mathematical Vulnerabilities**
- **Gas Exhaustion**: Unbounded while loop
- **Precision Accumulation**: Multiple rounding operations
- **Fee Calculation Errors**: Complex fee application logic
- **Edge Case Failures**: Could fail with extreme parameters

### **3. User Fund Impact**
- **Direct**: Determines withdrawal amounts from Reservoir strategy
- **Severity**: High - affects all Reservoir withdrawals
- **Scope**: All users withdrawing from Reservoir positions

## Exploitation Scenarios

### **Scenario 1: Gas Exhaustion Attack**
1. Attacker manipulates price/fee parameters to extreme values
2. Initial estimate becomes very inaccurate
3. While loop requires excessive iterations
4. **Impact**: Transaction failure due to gas limits

### **Scenario 2: Precision Loss Exploitation**
1. Attacker uses edge case parameters that amplify rounding errors
2. Multiple precision operations accumulate errors
3. Final withdrawal amount is significantly incorrect
4. **Impact**: Users receive less than expected assets

### **Scenario 3: Fee Manipulation**
1. If Reservoir protocol parameters change during calculation
2. Fee calculations become inconsistent
3. Withdrawal calculations fail or return wrong amounts
4. **Impact**: Failed withdrawals or economic loss

## Economic Impact
- **Severity**: HIGH
- **User Funds at Risk**: All funds in Reservoir strategy
- **Attack Cost**: Low to Medium - depends on external protocol manipulation
- **Profit Potential**: Medium - incorrect withdrawal calculations

## Technical Details
- **Function Type**: Public pure function
- **Access Control**: None (pure calculation)
- **Dependencies**: 
  - RESERVOIR_PRICE_PRECISION constant
  - RESERVOIR_FEE_PRECISION constant
  - _previewRedeem internal function
  - Math.ceilDiv from OpenZeppelin
- **Gas Usage**: Variable - depends on initial estimate accuracy
- **Mathematical Operations**: 4+ precision-sensitive calculations

## Constants Used
- `RESERVOIR_PRICE_PRECISION`: Used for price scaling
- `RESERVOIR_FEE_PRECISION = 1e6`: Used for fee calculations
- Note: The interaction between these precision constants is critical for correctness

## Usage in Protocol
Called by withdraw function to determine redemption amounts:
```solidity
params.assetsToWithdraw = getAssetsToWithdraw({
    _shares: params.shares,
    _currentPrice: ISavingModule(savingModule).currentPrice(),
    _redeemFee: ISavingModule(savingModule).redeemFee()
});
```

Any errors in this function directly impact user withdrawal amounts. 