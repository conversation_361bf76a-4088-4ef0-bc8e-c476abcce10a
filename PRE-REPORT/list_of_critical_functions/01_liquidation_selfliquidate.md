## Function: selfLiquidate

### 📍 **Location & Path**
- **File**: `src/LiquidationManager.sol`
- **Contract**: LiquidationManager
- **Line Numbers**: Lines 110 - 260
- **Full Path**: `jigsaw-protocol-v1/src/LiquidationManager.sol/selfLiquidate()`

### 📋 **Function Signature**
```solidity
function selfLiquidate(
    address _collateral,
    uint256 _jUsdAmount,
    SwapParamsCalldata calldata _swapParams,
    StrategiesParamsCalldata calldata _strategiesParams
)
    external
    override
    nonReentrant
    whenNotPaused
    validAddress(_collateral)
    validAmount(_jUsdAmount)
    returns (uint256 collateralUsed, uint256 jUsdAmountRepaid)
{
    // Initialize self-liquidation temporary data struct.
    SelfLiquidateTempData memory tempData = SelfLiquidateTempData({
        holdingManager: _getHoldingManager(),
        stablesManager: _getStablesManager(),
        swapManager: _getSwapManager(),
        holding: address(0),
        isRegistryActive: false,
        registryAddress: address(0),
        totalBorrowed: 0,
        totalAvailableCollateral: 0,
        totalRequiredCollateral: 0,
        totalSelfLiquidatableCollateral: 0,
        totalFeeCollateral: 0,
        jUsdAmountToBurn: 0,
        exchangeRate: 0,
        collateralInStrategies: 0,
        swapPath: _swapParams.swapPath,
        deadline: _swapParams.deadline,
        amountInMaximum: _swapParams.amountInMaximum,
        slippagePercentage: _swapParams.slippagePercentage,
        useHoldingBalance: _strategiesParams.useHoldingBalance,
        strategies: _strategiesParams.strategies,
        strategiesData: _strategiesParams.strategiesData
    });

    // Get precision for computations.
    uint256 precision = LIQUIDATION_PRECISION;

    // Get user's holding.
    tempData.holding = tempData.holdingManager.userHolding(msg.sender);

    // Ensure that user has a holding account in the system.
    require(tempData.holdingManager.isHolding(tempData.holding), "3002");

    // Ensure collateral registry is active.
    (tempData.isRegistryActive, tempData.registryAddress) = tempData.stablesManager.shareRegistryInfo(_collateral);
    require(tempData.isRegistryActive, "1200");

    // Ensure user is solvent.
    tempData.totalBorrowed = ISharesRegistry(tempData.registryAddress).borrowed(tempData.holding);
    require(tempData.stablesManager.isSolvent({ _token: _collateral, _holding: tempData.holding }), "3075");

    // Ensure self-liquidation amount <= borrowed.
    tempData.jUsdAmountToBurn = _jUsdAmount;
    require(tempData.jUsdAmountToBurn <= tempData.totalBorrowed, "2003");

    // Calculate the collateral required for self-liquidation.
    tempData.exchangeRate = ISharesRegistry(tempData.registryAddress).getExchangeRate();
    tempData.totalRequiredCollateral =
        _getCollateralForJUsd(_collateral, tempData.jUsdAmountToBurn, tempData.exchangeRate);

    // Ensure that amountInMaximum is within acceptable range specified by user.
    // See the interface for specs on `slippagePercentage`.

    // Ensure safe computation.
    require(_swapParams.slippagePercentage <= precision, "3081");
    if (
        tempData.amountInMaximum
            > tempData.totalRequiredCollateral
                + tempData.totalRequiredCollateral.mulDiv(_swapParams.slippagePercentage, precision)
    ) {
        revert("3078");
    }

    // Calculate the self-liquidation fee amount.
    tempData.totalFeeCollateral = tempData.amountInMaximum.mulDiv(selfLiquidationFee, precision, Math.Rounding.Ceil);
    // Calculate the total self-liquidatable collateral required to perform self-liquidation.
    tempData.totalSelfLiquidatableCollateral = tempData.amountInMaximum + tempData.totalFeeCollateral;

    // Retrieve collateral from strategies if needed.
    if (tempData.strategies.length > 0) {
        tempData.collateralInStrategies = _retrieveCollateral({
            _token: _collateral,
            _holding: tempData.holding,
            _amount: tempData.totalSelfLiquidatableCollateral,
            _strategies: tempData.strategies,
            _strategiesData: tempData.strategiesData,
            useHoldingBalance: tempData.useHoldingBalance
        });
    }

    // Set totalAvailableCollateral equal to retrieved collateral or holding's balance as user's specified.
    tempData.totalAvailableCollateral = !tempData.useHoldingBalance
        ? tempData.collateralInStrategies
        : IERC20Metadata(_collateral).balanceOf(tempData.holding);

    // Ensure there's enough available collateral to execute self-liquidation with specified amounts.
    require(tempData.totalAvailableCollateral >= tempData.totalSelfLiquidatableCollateral, "3076");

    // Swap collateral for jUSD.
    uint256 collateralUsedForSwap = tempData.swapManager.swapExactOutputMultihop({
        _tokenIn: _collateral,
        _swapPath: tempData.swapPath,
        _userHolding: tempData.holding,
        _deadline: tempData.deadline,
        _amountOut: tempData.jUsdAmountToBurn,
        _amountInMaximum: tempData.amountInMaximum
    });

    // Compute the final fee amount (if any) to be paid for performing self-liquidation.
    uint256 finalFeeCollateral = collateralUsedForSwap.mulDiv(selfLiquidationFee, precision, Math.Rounding.Ceil);

    // Transfer fees to fee address.
    if (finalFeeCollateral != 0) {
        IHolding(tempData.holding).transfer({
            _token: _collateral,
            _to: manager.feeAddress(),
            _amount: finalFeeCollateral
        });
    }

    // Save the jUSD amount that has been repaid.
    jUsdAmountRepaid = tempData.jUsdAmountToBurn;
    // Save the amount of collateral that has been used to repay jUSD.
    collateralUsed = collateralUsedForSwap + finalFeeCollateral;

    // Repay debt with jUsd obtained from Uniswap.
    tempData.stablesManager.repay({
        _holding: tempData.holding,
        _token: _collateral,
        _amount: jUsdAmountRepaid,
        _burnFrom: tempData.holding
    });

    // Remove collateral from holding.
    tempData.stablesManager.removeCollateral({
        _holding: tempData.holding,
        _token: _collateral,
        _amount: collateralUsed
    });

    // Emit event indicating self-liquidation.
    emit SelfLiquidated({
        holding: tempData.holding,
        token: _collateral,
        amount: jUsdAmountRepaid,
        collateralUsed: collateralUsed
    });
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: 
  - `_getHoldingManager()`, `_getStablesManager()`, `_getSwapManager()`
  - `_getCollateralForJUsd()` - Internal price calculation
  - `_retrieveCollateral()` - Strategy withdrawal handler
- **External Calls**: 
  - `ISharesRegistry(_registryAddress).borrowed()`, `.getExchangeRate()`
  - `SwapManager.swapExactOutputMultihop()` - Uniswap integration
  - `StablesManager.repay()`, `.removeCollateral()`
  - `IHolding(_holding).transfer()` - Fee transfer
- **State Changes**: 
  - Updates debt amount in SharesRegistry
  - Removes collateral from holding
  - Transfers fees to feeAddress
- **Events Emitted**: `SelfLiquidated`
- **Modifiers Applied**: `nonReentrant`, `whenNotPaused`, `validAddress`, `validAmount`

### 📖 **Function Summary**
**What it does:** I found this function allows users to self-liquidate their own debt position by using their collateral to swap for jUSD and repay their debt. It's designed to help users avoid being liquidated by external liquidators.

**Input Parameters:** 
- `_collateral`: Address of the collateral token to use for liquidation
- `_jUsdAmount`: Amount of jUSD debt to repay  
- `_swapParams`: Swap configuration (path, deadline, max amount, slippage)
- `_strategiesParams`: Strategy withdrawal configuration

**Return Values:** 
- `collateralUsed`: Total amount of collateral consumed (swap + fees)
- `jUsdAmountRepaid`: Amount of jUSD debt actually repaid

**Side Effects:** 
- Withdraws collateral from strategies if needed
- Swaps collateral for jUSD via Uniswap
- Burns jUSD to repay debt
- Transfers self-liquidation fees to protocol
- Updates collateral and debt accounting

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:**
This function implements the dynamic collateral feature where users can self-liquidate to maintain solvency. It should only work for solvent users and provides a way to reduce debt without external liquidation. The function integrates with the strategy system to retrieve collateral that may be actively earning yield in other protocols.

### ⚠️ **Critical Notes**
- **Risk Level**: High - Involves complex multi-step operations with external dependencies
- **Financial Impact**: Yes - Handles user debt repayment and collateral management
- **External Dependencies**: Uniswap for swaps, multiple strategies for collateral retrieval
- **Admin Privileges Required**: No - User-initiated function
- **Key Vulnerability Areas**: 
  - Oracle price manipulation during swap calculations
  - Strategy withdrawal failures
  - Reentrancy via external calls to strategies and swap manager
  - Slippage parameter validation
  - Fee calculation precision

--- 