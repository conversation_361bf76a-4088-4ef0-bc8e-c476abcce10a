## Function: getFeeAbsolute

### 📍 **Location & Path**
- **File**: `src/libraries/OperationsLib.sol`
- **Contract**: OperationsLib (Library)
- **Line Numbers**: Lines 22 - 24
- **Full Path**: `jigsaw-strategies-v1/src/libraries/OperationsLib.sol/getFeeAbsolute()`

### 📋 **Function Signature**
```solidity
function getFeeAbsolute(uint256 amount, uint256 fee) internal pure returns (uint256) {
    return (amount * fee) / FEE_FACTOR + (amount * fee % FEE_FACTOR == 0 ? 0 : 1);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: Used by `_takePerformanceFee()` in StrategyBaseUpgradeableV2
- **External Calls**: None - Pure mathematical calculation
- **State Changes**: None - Pure function
- **Constants Used**: `FEE_FACTOR = 10_000` (represents 100% = 10,000 basis points)
- **Mathematical Operations**: 
  - Multiplication: `amount * fee`
  - Division: `(amount * fee) / FEE_FACTOR`
  - Modulo: `amount * fee % FEE_FACTOR`
  - Conditional: Adds 1 if remainder exists (rounding up)

### 📖 **Function Summary**
**What it does:** I found this function calculates performance fees using basis points with ceiling rounding. It multiplies the amount by fee percentage (in basis points) and always rounds UP to avoid precision loss, as noted in the comment.

**Input Parameters:** 
- `amount`: The original amount to apply the fee on
- `fee`: The fee percentage in basis points (e.g., 500 = 5%)

**Return Values:** 
- `uint256`: The calculated fee value (always rounded up)

**Side Effects:** None - Pure mathematical calculation

### ⚠️ **CRITICAL MATHEMATICAL VULNERABILITIES**

**1. Integer Overflow in Multiplication:**
- Calculation: `amount * fee`
- No overflow protection for large amounts
- **Example**: `type(uint256).max * 10000` would overflow
- **Impact**: Transaction reverts or unpredictable behavior

**2. Always Rounds Up Fee:**
- Intentional ceiling rounding: `+ (amount * fee % FEE_FACTOR == 0 ? 0 : 1)`
- Always favors the protocol over users
- **Impact**: Users pay slightly more fees than mathematically exact

**3. No Input Validation:**
- No check that `fee <= FEE_FACTOR` (100%)
- Could accept fees > 100% (fee > 10,000)
- **Impact**: Could charge more than 100% fee if misconfigured

---

## Function: getRatio

### 📍 **Location & Path**
- **File**: `src/libraries/OperationsLib.sol`
- **Contract**: OperationsLib (Library)
- **Line Numbers**: Lines 31 - 50
- **Full Path**: `jigsaw-strategies-v1/src/libraries/OperationsLib.sol/getRatio()`

### 📋 **Function Signature**
```solidity
function getRatio(
    uint256 numerator,
    uint256 denominator,
    uint256 precision,
    Rounding rounding
) internal pure returns (uint256) {
    if (numerator == 0 || denominator == 0) {
        return 0;
    }

    uint256 _numerator = numerator * 10 ** precision;
    uint256 _quotient = _numerator / denominator;

    // Round up if necessary
    if (rounding == Rounding.Ceil && _numerator % denominator > 0) {
        _quotient += 1;
    }

    return (_quotient);
}
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: Used throughout strategies for ratio calculations
- **External Calls**: None - Pure mathematical calculation
- **State Changes**: None - Pure function
- **Mathematical Operations**: 
  - Multiplication: `numerator * 10 ** precision`
  - Division: `_numerator / denominator`
  - Modulo: `_numerator % denominator`
  - Exponentiation: `10 ** precision`
  - Conditional rounding based on enum

### 📖 **Function Summary**
**What it does:** I found this function calculates ratios with specified decimal precision and rounding direction. It scales the numerator by the precision factor before division and optionally rounds up.

**Input Parameters:** 
- `numerator`: The numerator in the ratio calculation
- `denominator`: The denominator in the ratio calculation  
- `precision`: The number of decimals to include in the result
- `rounding`: The rounding direction (Ceil or Floor)

**Return Values:** 
- `uint256`: The calculated ratio scaled by precision

**Side Effects:** None - Pure mathematical calculation

### ⚠️ **CRITICAL MATHEMATICAL VULNERABILITIES**

**1. Integer Overflow in Precision Scaling:**
- Calculation: `numerator * 10 ** precision`
- No overflow protection for large numerators or high precision
- **Example**: Large numerator with high precision could overflow
- **Impact**: Transaction reverts or incorrect ratio calculation

**2. Division by Zero Protection Issue:**
- Returns 0 when `numerator == 0 || denominator == 0`
- But `denominator == 0` should be an error, not return 0
- **Impact**: Hides division by zero errors that should fail

**3. Precision Loss Despite Scaling:**
- Division truncates: `_numerator / denominator`
- Only rounds up if `Rounding.Ceil` AND remainder exists
- **Impact**: Floor rounding can lose precision

**4. Unchecked Precision Parameter:**
- No limit on `precision` value
- Could cause overflow in `10 ** precision`
- **Impact**: DoS via overflow or excessive gas consumption

### 🚨 **EXPLOITATION SCENARIOS**

**Scenario 1: Fee Overflow Attack**
1. Strategy holds very large token amounts
2. Fee calculation: `largeAmount * fee` overflows
3. Transaction reverts, preventing withdrawals
4. **Result**: DoS attack, funds locked

**Scenario 2: Zero Denominator Silent Failure**
1. Attacker manipulates ratio calculation to have zero denominator
2. `getRatio()` returns 0 instead of failing
3. Downstream calculations use incorrect 0 ratio
4. **Result**: Incorrect financial calculations

**Scenario 3: Precision Overflow Attack**
1. Attacker calls function with very high precision value
2. `10 ** precision` overflows or consumes excessive gas
3. Transaction fails or becomes too expensive
4. **Result**: DoS attack on ratio calculations

**Scenario 4: Fee Ceiling Rounding Exploitation**
1. Multiple small fee calculations with ceiling rounding
2. Each operation rounds up slightly
3. Accumulated over many operations
4. **Result**: Users pay more fees than intended

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:**
These mathematical functions are core to the fee system and strategy accounting. They should provide accurate calculations while preventing precision loss vulnerabilities that could be exploited.

### ⚠️ **Critical Notes**
- **Risk Level**: High - Core mathematical functions used in fee calculations
- **Financial Impact**: Yes - Fee miscalculations directly affect user funds
- **External Dependencies**: None - Pure mathematical operations
- **Admin Privileges Required**: No - Library functions called by strategies
- **Key Vulnerability Areas**: 
  - Integer overflow in multiplication operations
  - No input validation on fee percentages or precision values
  - Silent failure on division by zero in getRatio
  - Always-ceiling rounding potentially overcharging users
  - No protection against excessive precision values

--- 