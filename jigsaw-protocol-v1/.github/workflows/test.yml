name: test

on:
  pull_request:
  workflow_dispatch:

env:
  FOUNDRY_PROFILE: ci

jobs:
  check:
    strategy:
      fail-fast: true

    name: Foundry project
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          version: nightly

      - name: Run Forge build
        run: |
          forge --version
          forge build --sizes
        id: build

      - name: create env file
        run: |
          touch .env
          echo MAINNET_RPC_URL=${{ secrets.MAINNET_RPC_URL }} >> .env
          echo ARBITRUM_RPC_URL=${{ secrets.ARBITRUM_RPC_URL }} >> .env
          echo DEPLOYER_PRIVATE_KEY=${{ secrets.DEPLOYER_PRIVATE_KEY }} >> .env

      - name: Run Forge tests
        run: |
          forge test --fuzz-runs 100 -vvv
        id: test
